//
//  StringExtension.swift
//  Appio
//
//  Created by gondo on 07/04/2025.
//

import SwiftUI

extension String {
    var toTextAlignment: TextAlignment {
        switch lowercased() {
        case "leading": .leading
        case "trailing": .trailing
        default: .center
        }
    }

    var toVerticalAlignment: VerticalAlignment {
        switch lowercased() {
        case "top": .top
        case "bottom": .bottom
        default: .center
        }
    }

    var toHorizontalAlignment: HorizontalAlignment {
        switch lowercased() {
        case "leading": .leading
        case "trailing": .trailing
        default: .center
        }
    }

    var toAlignment: Alignment {
        switch lowercased() {
        case "topleading": .topLeading
        case "top": .top
        case "toptrailing": .topTrailing
        case "leading": .leading
        case "center": .center
        case "trailing": .trailing
        case "bottomleading": .bottomLeading
        case "bottom": .bottom
        case "bottomtrailing": .bottomTrailing
        default: .center
        }
    }

    // Docs: https://developer.apple.com/documentation/swiftui/font/weight
    var toFontWeight: Font.Weight {
        switch lowercased() {
        case "black": .black
        case "bold": .bold
        case "heavy": .heavy
        case "light": .light
        case "medium": .medium
        case "regular": .regular
        case "semibold": .semibold
        case "thin": .thin
        case "ultralight": .ultraLight
        default: .regular
        }
    }

    var toContentMode: ContentMode {
        switch lowercased() {
        case "fit": .fit
        case "fill": .fill
        default: .fit
        }
    }

    var toGaugeStyle: AnyGaugeStyle {
        switch lowercased() {
        case "linearcapacity": AnyGaugeStyle(.linearCapacity)
        case "accessorycircular": AnyGaugeStyle(.accessoryCircular)
        case "accessorylinear": AnyGaugeStyle(.accessoryLinear)
        case "accessorycircularcapacity": AnyGaugeStyle(.accessoryCircularCapacity)
        case "accessorylinearcapacity": AnyGaugeStyle(.accessoryLinearCapacity)
        default: AnyGaugeStyle(.automatic)
        }
    }
}

struct AnyGaugeStyle: GaugeStyle {
    private let style: any GaugeStyle

    init(_ style: any GaugeStyle) {
        self.style = style
    }

    func makeBody(configuration: Configuration) -> some View {
        AnyView(style.makeBody(configuration: configuration))
    }
}
