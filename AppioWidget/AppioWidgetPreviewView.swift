//
//  AppioWidgetPreviewView.swift
//  Appio
//
//  Created by gondo on 15/09/2025.
//

import SwiftUI
import WidgetKit

struct AppioWidgetPreviewView: View {
    @Environment(\.widgetFamily) var family
    
    var body: some View {
        GeometryReader { geometry in
            VStack(alignment: .center, spacing: 12) {
                Image(systemName: "widget.\(familyToImageSystemName())")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: geometry.size.width / 3, height: geometry.size.height / 3)
                    .fontWeight(.thin)

                Text(family.humanDescription)
                    .font(.system(size: (geometry.size.width + geometry.size.height) / 15))
            }
            .frame(width: geometry.size.width, height: geometry.size.height)
        }
    }
    
    private func familyToImageSystemName() -> String {
        switch family {
        case .systemSmall:
            return "small"
        case .systemMedium:
            return "medium"
        case .systemLarge:
            return "large"
        case .systemExtraLarge:
            return "extralarge"
        case .accessoryCircular, .accessoryRectangular, .accessoryInline:
            return "small"
        @unknown default:
            return "small"
        }
    }
}

private struct PreviewNoServicesView: View {
    var body: some View {
        Image("LaunchImage")
            .resizable()
            .aspectRatio(contentMode: .fit)
            .frame(width: 42, height: 42)
            .opacity(0.2)
    }
}

#if DEBUG
#Preview("Preview Widget Small", as: .systemSmall) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.preview()
}

#Preview("Preview Widget Medium", as: .systemMedium) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.preview()
}


#Preview("Preview Widget Large", as: .systemLarge) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.preview()
}
#endif
