//
//  AppioWidgetEmptyView.swift
//  Appio
//
//  Created by gondo on 15/09/2025.
//

import SwiftUI
import WidgetKit

struct AppioWidgetEmptyView: View {
    private let services = StorageManager.services

    var body: some View {
        ZStack {
            if services.isEmpty {
                EmptyNoServicesView()
            } else {
                EmptyServicesLogosView(services: services)
            }
        }
        .padding()
    }
}

struct EmptyNoServicesView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Spacer()
            
            // Header with orange lightbulb and title
            HStack(spacing: 8) {
                Image(systemName: "lightbulb.circle.fill")
                    .font(.system(size: 20))
                    .foregroundColor(.orange)
                    .frame(width: 20)
                
                Text("Setup Widget")
                    .font(.system(size: 13, weight: .bold))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            // Divider
            HStack {
                Rectangle()
                    .fill(.gray.opacity(0.2))
                    .frame(height: 0.5)
            }
            
            // Step 1
            HStack(alignment: .center, spacing: 8) {
                Image(systemName: "rectangle.and.hand.point.up.left.filled")
                    .font(.system(size: 14))
                    .foregroundColor(.blue)
                    .frame(width: 20)
                
                Text("Tap to start")
                    .font(.system(size: 12))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            Spacer()
        }
        .fixedSize(horizontal: true, vertical: false)
    }
}

struct EmptyServicesLogosView: View {
    let services: [ServiceEntity]

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Spacer()
            
            // Services logos
            if !services.isEmpty {
                HStack(spacing: 6) {
                    // Max 5 logos
                    ForEach(services.prefix(5), id: \.id) { service in
                        if let logoURL = URL(string: service.logoURL) {
                            Image.cached(url: logoURL)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 24, height: 24)
                                .clipShape(RoundedRectangle(cornerRadius: 5))
                        }
                    }
                }
                .padding(.bottom, 6)
            }
            
            // Header with orange lightbulb and title
            HStack(spacing: 8) {
                Image(systemName: "lightbulb.circle.fill")
                    .font(.system(size: 20))
                    .foregroundColor(.orange)
                    .frame(width: 16)
                
                Text("Setup Widget")
                    .font(.system(size: 13, weight: .bold))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            // Divider
            Rectangle()
                .fill(.gray.opacity(0.2))
                .frame(height: 0.5)
            
            // Step 1
            HStack(alignment: .center, spacing: 8) {
                Image(systemName: "rectangle.and.hand.point.up.left.filled")
                    .font(.system(size: 14))
                    .foregroundColor(.blue)
                    .frame(width: 16)
                
                Text("Long press here")
                    .font(.system(size: 12))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            // Divider
            Rectangle()
                .fill(.gray.opacity(0.2))
                .frame(height: 0.5)
            
            // Step 2
            HStack(alignment: .center, spacing: 8) {
                Image(systemName: "info.circle")
                    .font(.system(size: 14))
                    .foregroundColor(.blue)
                    .frame(width: 16)
                
                Text("Edit Widget")
                    .font(.system(size: 12))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            Spacer()
        }
        .fixedSize(horizontal: true, vertical: false)
    }
}

#if DEBUG
#Preview("Empty Widget", as: .systemMedium) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.empty()
}

#Preview("Empty Widget Small", as: .systemSmall) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.empty()
}

#Preview("Empty Widget Large", as: .systemLarge) {
    AppioWidget()
} timeline: {
    AppioWidgetEntry.empty()
}
#endif
