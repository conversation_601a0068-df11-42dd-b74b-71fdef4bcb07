<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>so.appio.app.refresh</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>so.appio.app</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>appio</string>
			</array>
		</dict>
	</array>
	<key>NSUserActivityTypes</key>
	<array>
		<string>ConfigurationIntentIntent</string>
	</array>
	<key>OSLogPreferences</key>
	<dict>
		<key>so.appio.app.logger</key>
		<dict>
			<key>appio</key>
			<dict>
				<key>Enable-Private-Data</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>fetch</string>
		<string>audio</string>
	</array>
    <key>SENTRY_DSN</key>
    <string>$(SENTRY_DSN)</string>
</dict>
</plist>
