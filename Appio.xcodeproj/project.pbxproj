// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		921F01652D79BE4F0028254A /* opening-flow.md in Resources */ = {isa = PBXBuildFile; fileRef = 921F01642D79BE480028254A /* opening-flow.md */; };
		922E4B4A2DA011530069FF7E /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 924DCB6E2D9EE13B00F7831B /* WidgetKit.framework */; };
		922E4B4B2DA011530069FF7E /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 924DCB702D9EE13B00F7831B /* SwiftUI.framework */; };
		922E4B5A2DA011560069FF7E /* AppioWidgetExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 922E4B492DA011530069FF7E /* AppioWidgetExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		923DA5992CCA953E00630F93 /* README.md in Resources */ = {isa = PBXBuildFile; fileRef = 923DA5982CCA953E00630F93 /* README.md */; };
		9244A0792D81AC6E009F9C10 /* AppioNotificationServiceExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 9244A0722D81AC6E009F9C10 /* AppioNotificationServiceExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		9244A0862D81B028009F9C10 /* UserNotifications.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9244A0852D81B028009F9C10 /* UserNotifications.framework */; };
		9244A0882D81B028009F9C10 /* UserNotificationsUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9244A0872D81B028009F9C10 /* UserNotificationsUI.framework */; };
		9244A0922D81B028009F9C10 /* AppioNotificationContentExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 9244A0832D81B028009F9C10 /* AppioNotificationContentExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		9264253B2D479DC200EE1630 /* install-button-ios-versions.txt in Resources */ = {isa = PBXBuildFile; fileRef = 926425332D479DC200EE1630 /* install-button-ios-versions.txt */; };
		9264253C2D479DC200EE1630 /* Smart App Banner - OPEN.PNG in Resources */ = {isa = PBXBuildFile; fileRef = 926425382D479DC200EE1630 /* Smart App Banner - OPEN.PNG */; };
		9264253D2D479DC200EE1630 /* Smart App Banner - Download.PNG in Resources */ = {isa = PBXBuildFile; fileRef = 926425362D479DC200EE1630 /* Smart App Banner - Download.PNG */; };
		9264253E2D479DC200EE1630 /* Smart App Banner.md in Resources */ = {isa = PBXBuildFile; fileRef = 926425342D479DC200EE1630 /* Smart App Banner.md */; };
		9264253F2D479DC200EE1630 /* Smart App Banner - GET.PNG in Resources */ = {isa = PBXBuildFile; fileRef = 926425372D479DC200EE1630 /* Smart App Banner - GET.PNG */; };
		926425402D479DC200EE1630 /* Smart App Banner.MP4 in Resources */ = {isa = PBXBuildFile; fileRef = 926425352D479DC200EE1630 /* Smart App Banner.MP4 */; };
		926425422D479DDC00EE1630 /* api.md in Resources */ = {isa = PBXBuildFile; fileRef = 926425412D479DD700EE1630 /* api.md */; };
		926425442D479E4C00EE1630 /* app-clip.md in Resources */ = {isa = PBXBuildFile; fileRef = 926425432D479E4800EE1630 /* app-clip.md */; };
		926425462D479E7100EE1630 /* clipboard-copy-paste.md in Resources */ = {isa = PBXBuildFile; fileRef = 926425452D479E6500EE1630 /* clipboard-copy-paste.md */; };
		9264254A2D479EC300EE1630 /* ios-sizes.md in Resources */ = {isa = PBXBuildFile; fileRef = 926425492D479EC000EE1630 /* ios-sizes.md */; };
		9264254D2D47A06300EE1630 /* deep-linking.md in Resources */ = {isa = PBXBuildFile; fileRef = 9264254C2D47A05F00EE1630 /* deep-linking.md */; };
		926915C92E0467E50075ED37 /* ios-widget-config.md in Resources */ = {isa = PBXBuildFile; fileRef = 926915C82E0467E50075ED37 /* ios-widget-config.md */; };
		927205582E1EAF1000A99E4B /* release.md in Resources */ = {isa = PBXBuildFile; fileRef = 927205572E1EAF0C00A99E4B /* release.md */; };
		927A88422E7A239400D424E8 /* Sentry in Frameworks */ = {isa = PBXBuildFile; productRef = 927A88412E7A239400D424E8 /* Sentry */; };
		92D4A2872D81D663004153B9 /* push-notifications.md in Resources */ = {isa = PBXBuildFile; fileRef = 92D4A2862D81D663004153B9 /* push-notifications.md */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		922E4B582DA011560069FF7E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 92739DD12CC7AA1B00F5B1CF /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 922E4B482DA011530069FF7E;
			remoteInfo = AppioWidgetExtension;
		};
		9244A0772D81AC6E009F9C10 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 92739DD12CC7AA1B00F5B1CF /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9244A0712D81AC6E009F9C10;
			remoteInfo = AppioNotificationServiceExtension;
		};
		9244A0902D81B028009F9C10 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 92739DD12CC7AA1B00F5B1CF /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9244A0822D81B028009F9C10;
			remoteInfo = AppioNotificationContentExtension;
		};
		92739DEA2CC7AA1E00F5B1CF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 92739DD12CC7AA1B00F5B1CF /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 92739DD82CC7AA1B00F5B1CF;
			remoteInfo = "Appio App";
		};
		92739DF42CC7AA1E00F5B1CF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 92739DD12CC7AA1B00F5B1CF /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 92739DD82CC7AA1B00F5B1CF;
			remoteInfo = "Appio App";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		9244A07E2D81AC6E009F9C10 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				922E4B5A2DA011560069FF7E /* AppioWidgetExtension.appex in Embed Foundation Extensions */,
				9244A0792D81AC6E009F9C10 /* AppioNotificationServiceExtension.appex in Embed Foundation Extensions */,
				9244A0922D81B028009F9C10 /* AppioNotificationContentExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		921F01642D79BE480028254A /* opening-flow.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = "opening-flow.md"; sourceTree = "<group>"; };
		922E4B492DA011530069FF7E /* AppioWidgetExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = AppioWidgetExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		923DA5982CCA953E00630F93 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		9244A0722D81AC6E009F9C10 /* AppioNotificationServiceExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = AppioNotificationServiceExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		9244A0832D81B028009F9C10 /* AppioNotificationContentExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = AppioNotificationContentExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		9244A0852D81B028009F9C10 /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		9244A0872D81B028009F9C10 /* UserNotificationsUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotificationsUI.framework; path = System/Library/Frameworks/UserNotificationsUI.framework; sourceTree = SDKROOT; };
		924DCB6E2D9EE13B00F7831B /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		924DCB702D9EE13B00F7831B /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		926425332D479DC200EE1630 /* install-button-ios-versions.txt */ = {isa = PBXFileReference; lastKnownFileType = text; path = "install-button-ios-versions.txt"; sourceTree = "<group>"; };
		926425342D479DC200EE1630 /* Smart App Banner.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = "Smart App Banner.md"; sourceTree = "<group>"; };
		926425352D479DC200EE1630 /* Smart App Banner.MP4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Smart App Banner.MP4"; sourceTree = "<group>"; };
		926425362D479DC200EE1630 /* Smart App Banner - Download.PNG */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Smart App Banner - Download.PNG"; sourceTree = "<group>"; };
		926425372D479DC200EE1630 /* Smart App Banner - GET.PNG */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Smart App Banner - GET.PNG"; sourceTree = "<group>"; };
		926425382D479DC200EE1630 /* Smart App Banner - OPEN.PNG */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Smart App Banner - OPEN.PNG"; sourceTree = "<group>"; };
		926425412D479DD700EE1630 /* api.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = api.md; sourceTree = "<group>"; };
		926425432D479E4800EE1630 /* app-clip.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = "app-clip.md"; sourceTree = "<group>"; };
		926425452D479E6500EE1630 /* clipboard-copy-paste.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = "clipboard-copy-paste.md"; sourceTree = "<group>"; };
		926425492D479EC000EE1630 /* ios-sizes.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = "ios-sizes.md"; sourceTree = "<group>"; };
		9264254C2D47A05F00EE1630 /* deep-linking.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = "deep-linking.md"; sourceTree = "<group>"; };
		926915C82E0467E50075ED37 /* ios-widget-config.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = "ios-widget-config.md"; sourceTree = "<group>"; };
		927205572E1EAF0C00A99E4B /* release.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = release.md; sourceTree = "<group>"; };
		92739DD92CC7AA1B00F5B1CF /* Appio.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Appio.app; sourceTree = BUILT_PRODUCTS_DIR; };
		92739DE92CC7AA1E00F5B1CF /* AppioTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = AppioTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		92739DF32CC7AA1E00F5B1CF /* AppioUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = AppioUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		92739E0E2CCA890700F5B1CF /* Appio-App-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "Appio-App-Info.plist"; sourceTree = "<group>"; };
		92D4A2862D81D663004153B9 /* push-notifications.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = "push-notifications.md"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		921960C32E7AE21D0003656D /* Exceptions for "Configs" folder in "AppioNotificationServiceExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Base.xcconfig,
				Debug.xcconfig,
				Release.xcconfig,
			);
			target = 9244A0712D81AC6E009F9C10 /* AppioNotificationServiceExtension */;
		};
		921960C42E7AE21D0003656D /* Exceptions for "Configs" folder in "AppioNotificationContentExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Base.xcconfig,
				Debug.xcconfig,
				Release.xcconfig,
			);
			target = 9244A0822D81B028009F9C10 /* AppioNotificationContentExtension */;
		};
		921960C52E7AE21D0003656D /* Exceptions for "Configs" folder in "AppioWidgetExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Base.xcconfig,
				Debug.xcconfig,
				Release.xcconfig,
			);
			target = 922E4B482DA011530069FF7E /* AppioWidgetExtension */;
		};
		922E4B5B2DA011560069FF7E /* Exceptions for "AppioWidget" folder in "AppioWidgetExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 922E4B482DA011530069FF7E /* AppioWidgetExtension */;
		};
		922E4B842DA1EBC20069FF7E /* Exceptions for "Appio App" folder in "AppioWidgetExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Assets.xcassets,
				CoreData/Appio.xcdatamodeld,
				Entities/DeviceEntity.swift,
				Entities/FeatureFlagsEntity.swift,
				Entities/NotificationEntity.swift,
				Entities/ServiceEntity.swift,
				Entities/WidgetEntity.swift,
				Extensions/ColorExtension.swift,
				Extensions/DateExtension.swift,
				Extensions/StringExtension.swift,
				Extensions/UIDeviceExtension.swift,
				Managers/ImageManager.swift,
				Managers/StorageManager.swift,
				Managers/WidgetsManager.swift,
				Network/APIClient.swift,
				Network/ApiError.swift,
				Network/APIService.swift,
				Network/Models/APIDevice.swift,
				Network/Models/APIFeatureFlag.swift,
				Network/Models/APIFeedback.swift,
				Network/Models/APIFingerprint.swift,
				Network/Models/APINotification.swift,
				Network/Models/APIServiceStruct.swift,
				Network/Models/APIWidget.swift,
				UIConstants.swift,
				Utilities/AnyDecodable.swift,
				Utilities/AppVersion.swift,
				Utilities/Log.swift,
				Utilities/Sentry.swift,
			);
			target = 922E4B482DA011530069FF7E /* AppioWidgetExtension */;
		};
		923E8F0E2DA5417E00AA4A5E /* Exceptions for "AppioWidget" folder in "Appio" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				WidgetShared.swift,
			);
			target = 92739DD82CC7AA1B00F5B1CF /* Appio */;
		};
		9244A07A2D81AC6E009F9C10 /* Exceptions for "AppioNotificationServiceExtension" folder in "AppioNotificationServiceExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 9244A0712D81AC6E009F9C10 /* AppioNotificationServiceExtension */;
		};
		9244A0932D81B028009F9C10 /* Exceptions for "AppioNotificationContentExtension" folder in "AppioNotificationContentExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 9244A0822D81B028009F9C10 /* AppioNotificationContentExtension */;
		};
		928BC0C52D8AD97A00449E5F /* Exceptions for "Appio App" folder in "AppioNotificationServiceExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				CoreData/Appio.xcdatamodeld,
				Entities/DeviceEntity.swift,
				Entities/FeatureFlagsEntity.swift,
				Entities/NotificationEntity.swift,
				Entities/ServiceEntity.swift,
				Entities/WidgetEntity.swift,
				Extensions/UIDeviceExtension.swift,
				Managers/NotificationStorer.swift,
				Managers/StorageManager.swift,
				Network/Models/APIDevice.swift,
				Network/Models/APIFeatureFlag.swift,
				Network/Models/APIFeedback.swift,
				Network/Models/APINotification.swift,
				Network/Models/APIServiceStruct.swift,
				Network/Models/APIWidget.swift,
				Utilities/AnyDecodable.swift,
				Utilities/AppVersion.swift,
				Utilities/Log.swift,
				Utilities/Sentry.swift,
			);
			target = 9244A0712D81AC6E009F9C10 /* AppioNotificationServiceExtension */;
		};
		928BC0C62D8AD97A00449E5F /* Exceptions for "Appio App" folder in "AppioNotificationContentExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				CoreData/Appio.xcdatamodeld,
				Entities/DeviceEntity.swift,
				Entities/FeatureFlagsEntity.swift,
				Entities/NotificationEntity.swift,
				Entities/ServiceEntity.swift,
				Entities/WidgetEntity.swift,
				Extensions/UIDeviceExtension.swift,
				Managers/StorageManager.swift,
				Network/Models/APIDevice.swift,
				Network/Models/APIFeatureFlag.swift,
				Network/Models/APIFeedback.swift,
				Network/Models/APINotification.swift,
				Network/Models/APIServiceStruct.swift,
				Network/Models/APIWidget.swift,
				Utilities/AnyDecodable.swift,
				Utilities/AppVersion.swift,
				Utilities/Log.swift,
				Utilities/Sentry.swift,
			);
			target = 9244A0822D81B028009F9C10 /* AppioNotificationContentExtension */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		92029ED72D7769E700F18741 /* Scripts */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Scripts;
			sourceTree = "<group>";
		};
		921960B02E7AE0840003656D /* Configs */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				921960C32E7AE21D0003656D /* Exceptions for "Configs" folder in "AppioNotificationServiceExtension" target */,
				921960C42E7AE21D0003656D /* Exceptions for "Configs" folder in "AppioNotificationContentExtension" target */,
				921960C52E7AE21D0003656D /* Exceptions for "Configs" folder in "AppioWidgetExtension" target */,
			);
			path = Configs;
			sourceTree = "<group>";
		};
		922E4B4C2DA011530069FF7E /* AppioWidget */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				923E8F0E2DA5417E00AA4A5E /* Exceptions for "AppioWidget" folder in "Appio" target */,
				922E4B5B2DA011560069FF7E /* Exceptions for "AppioWidget" folder in "AppioWidgetExtension" target */,
			);
			path = AppioWidget;
			sourceTree = "<group>";
		};
		9244A0732D81AC6E009F9C10 /* AppioNotificationServiceExtension */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				9244A07A2D81AC6E009F9C10 /* Exceptions for "AppioNotificationServiceExtension" folder in "AppioNotificationServiceExtension" target */,
			);
			path = AppioNotificationServiceExtension;
			sourceTree = "<group>";
		};
		9244A0892D81B028009F9C10 /* AppioNotificationContentExtension */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				9244A0932D81B028009F9C10 /* Exceptions for "AppioNotificationContentExtension" folder in "AppioNotificationContentExtension" target */,
			);
			path = AppioNotificationContentExtension;
			sourceTree = "<group>";
		};
		927205592E1EAF7500A99E4B /* fastlane */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = fastlane;
			sourceTree = "<group>";
		};
		92739DDB2CC7AA1B00F5B1CF /* Appio App */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				928BC0C52D8AD97A00449E5F /* Exceptions for "Appio App" folder in "AppioNotificationServiceExtension" target */,
				928BC0C62D8AD97A00449E5F /* Exceptions for "Appio App" folder in "AppioNotificationContentExtension" target */,
				922E4B842DA1EBC20069FF7E /* Exceptions for "Appio App" folder in "AppioWidgetExtension" target */,
			);
			path = "Appio App";
			sourceTree = "<group>";
		};
		92739DEC2CC7AA1E00F5B1CF /* Appio AppTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Appio AppTests";
			sourceTree = "<group>";
		};
		92739DF62CC7AA1E00F5B1CF /* Appio AppUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Appio AppUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		922E4B462DA011530069FF7E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				922E4B4B2DA011530069FF7E /* SwiftUI.framework in Frameworks */,
				922E4B4A2DA011530069FF7E /* WidgetKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9244A06F2D81AC6E009F9C10 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9244A0802D81B028009F9C10 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9244A0882D81B028009F9C10 /* UserNotificationsUI.framework in Frameworks */,
				9244A0862D81B028009F9C10 /* UserNotifications.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		92739DD62CC7AA1B00F5B1CF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				927A88422E7A239400D424E8 /* Sentry in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		92739DE62CC7AA1E00F5B1CF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		92739DF02CC7AA1E00F5B1CF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		9244A0842D81B028009F9C10 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				9244A0852D81B028009F9C10 /* UserNotifications.framework */,
				9244A0872D81B028009F9C10 /* UserNotificationsUI.framework */,
				924DCB6E2D9EE13B00F7831B /* WidgetKit.framework */,
				924DCB702D9EE13B00F7831B /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		926425392D479DC200EE1630 /* Smart App Banner */ = {
			isa = PBXGroup;
			children = (
				926425332D479DC200EE1630 /* install-button-ios-versions.txt */,
				926425342D479DC200EE1630 /* Smart App Banner.md */,
				926425352D479DC200EE1630 /* Smart App Banner.MP4 */,
				926425362D479DC200EE1630 /* Smart App Banner - Download.PNG */,
				926425372D479DC200EE1630 /* Smart App Banner - GET.PNG */,
				926425382D479DC200EE1630 /* Smart App Banner - OPEN.PNG */,
			);
			path = "Smart App Banner";
			sourceTree = "<group>";
		};
		9264253A2D479DC200EE1630 /* Docs */ = {
			isa = PBXGroup;
			children = (
				927205572E1EAF0C00A99E4B /* release.md */,
				926915C82E0467E50075ED37 /* ios-widget-config.md */,
				92D4A2862D81D663004153B9 /* push-notifications.md */,
				921F01642D79BE480028254A /* opening-flow.md */,
				9264254C2D47A05F00EE1630 /* deep-linking.md */,
				926425492D479EC000EE1630 /* ios-sizes.md */,
				926425452D479E6500EE1630 /* clipboard-copy-paste.md */,
				926425432D479E4800EE1630 /* app-clip.md */,
				926425412D479DD700EE1630 /* api.md */,
				926425392D479DC200EE1630 /* Smart App Banner */,
			);
			path = Docs;
			sourceTree = "<group>";
		};
		92739DD02CC7AA1B00F5B1CF = {
			isa = PBXGroup;
			children = (
				921960B02E7AE0840003656D /* Configs */,
				927205592E1EAF7500A99E4B /* fastlane */,
				92029ED72D7769E700F18741 /* Scripts */,
				9264253A2D479DC200EE1630 /* Docs */,
				923DA5982CCA953E00630F93 /* README.md */,
				92739E0E2CCA890700F5B1CF /* Appio-App-Info.plist */,
				92739DDB2CC7AA1B00F5B1CF /* Appio App */,
				92739DEC2CC7AA1E00F5B1CF /* Appio AppTests */,
				92739DF62CC7AA1E00F5B1CF /* Appio AppUITests */,
				9244A0732D81AC6E009F9C10 /* AppioNotificationServiceExtension */,
				9244A0892D81B028009F9C10 /* AppioNotificationContentExtension */,
				922E4B4C2DA011530069FF7E /* AppioWidget */,
				9244A0842D81B028009F9C10 /* Frameworks */,
				92739DDA2CC7AA1B00F5B1CF /* Products */,
			);
			sourceTree = "<group>";
		};
		92739DDA2CC7AA1B00F5B1CF /* Products */ = {
			isa = PBXGroup;
			children = (
				92739DD92CC7AA1B00F5B1CF /* Appio.app */,
				92739DE92CC7AA1E00F5B1CF /* AppioTests.xctest */,
				92739DF32CC7AA1E00F5B1CF /* AppioUITests.xctest */,
				9244A0722D81AC6E009F9C10 /* AppioNotificationServiceExtension.appex */,
				9244A0832D81B028009F9C10 /* AppioNotificationContentExtension.appex */,
				922E4B492DA011530069FF7E /* AppioWidgetExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		922E4B482DA011530069FF7E /* AppioWidgetExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 922E4B5C2DA011560069FF7E /* Build configuration list for PBXNativeTarget "AppioWidgetExtension" */;
			buildPhases = (
				922E4B452DA011530069FF7E /* Sources */,
				922E4B462DA011530069FF7E /* Frameworks */,
				922E4B472DA011530069FF7E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				922E4B4C2DA011530069FF7E /* AppioWidget */,
			);
			name = AppioWidgetExtension;
			packageProductDependencies = (
			);
			productName = AppioWidgetExtension;
			productReference = 922E4B492DA011530069FF7E /* AppioWidgetExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		9244A0712D81AC6E009F9C10 /* AppioNotificationServiceExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9244A07B2D81AC6E009F9C10 /* Build configuration list for PBXNativeTarget "AppioNotificationServiceExtension" */;
			buildPhases = (
				9244A06E2D81AC6E009F9C10 /* Sources */,
				9244A06F2D81AC6E009F9C10 /* Frameworks */,
				9244A0702D81AC6E009F9C10 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				9244A0732D81AC6E009F9C10 /* AppioNotificationServiceExtension */,
			);
			name = AppioNotificationServiceExtension;
			packageProductDependencies = (
			);
			productName = AppioNotificationServiceExtension;
			productReference = 9244A0722D81AC6E009F9C10 /* AppioNotificationServiceExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		9244A0822D81B028009F9C10 /* AppioNotificationContentExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9244A0942D81B028009F9C10 /* Build configuration list for PBXNativeTarget "AppioNotificationContentExtension" */;
			buildPhases = (
				9244A07F2D81B028009F9C10 /* Sources */,
				9244A0802D81B028009F9C10 /* Frameworks */,
				9244A0812D81B028009F9C10 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				9244A0892D81B028009F9C10 /* AppioNotificationContentExtension */,
			);
			name = AppioNotificationContentExtension;
			packageProductDependencies = (
			);
			productName = AppioNotificationContentExtension;
			productReference = 9244A0832D81B028009F9C10 /* AppioNotificationContentExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		92739DD82CC7AA1B00F5B1CF /* Appio */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 92739DFD2CC7AA1E00F5B1CF /* Build configuration list for PBXNativeTarget "Appio" */;
			buildPhases = (
				92739DD52CC7AA1B00F5B1CF /* Sources */,
				92739DD62CC7AA1B00F5B1CF /* Frameworks */,
				92739DD72CC7AA1B00F5B1CF /* Resources */,
				9244A07E2D81AC6E009F9C10 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				9244A0782D81AC6E009F9C10 /* PBXTargetDependency */,
				9244A0912D81B028009F9C10 /* PBXTargetDependency */,
				922E4B592DA011560069FF7E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				92029ED72D7769E700F18741 /* Scripts */,
				921960B02E7AE0840003656D /* Configs */,
				92739DDB2CC7AA1B00F5B1CF /* Appio App */,
			);
			name = Appio;
			packageProductDependencies = (
				927A88412E7A239400D424E8 /* Sentry */,
			);
			productName = "Appio App";
			productReference = 92739DD92CC7AA1B00F5B1CF /* Appio.app */;
			productType = "com.apple.product-type.application";
		};
		92739DE82CC7AA1E00F5B1CF /* AppioTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 92739E002CC7AA1E00F5B1CF /* Build configuration list for PBXNativeTarget "AppioTests" */;
			buildPhases = (
				92739DE52CC7AA1E00F5B1CF /* Sources */,
				92739DE62CC7AA1E00F5B1CF /* Frameworks */,
				92739DE72CC7AA1E00F5B1CF /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				92739DEB2CC7AA1E00F5B1CF /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				92739DEC2CC7AA1E00F5B1CF /* Appio AppTests */,
			);
			name = AppioTests;
			packageProductDependencies = (
			);
			productName = "Appio AppTests";
			productReference = 92739DE92CC7AA1E00F5B1CF /* AppioTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		92739DF22CC7AA1E00F5B1CF /* AppioUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 92739E032CC7AA1E00F5B1CF /* Build configuration list for PBXNativeTarget "AppioUITests" */;
			buildPhases = (
				92739DEF2CC7AA1E00F5B1CF /* Sources */,
				92739DF02CC7AA1E00F5B1CF /* Frameworks */,
				92739DF12CC7AA1E00F5B1CF /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				92739DF52CC7AA1E00F5B1CF /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				92739DF62CC7AA1E00F5B1CF /* Appio AppUITests */,
			);
			name = AppioUITests;
			packageProductDependencies = (
			);
			productName = "Appio AppUITests";
			productReference = 92739DF32CC7AA1E00F5B1CF /* AppioUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		92739DD12CC7AA1B00F5B1CF /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 2600;
				TargetAttributes = {
					922E4B482DA011530069FF7E = {
						CreatedOnToolsVersion = 16.3;
					};
					9244A0712D81AC6E009F9C10 = {
						CreatedOnToolsVersion = 16.2;
					};
					9244A0822D81B028009F9C10 = {
						CreatedOnToolsVersion = 16.2;
					};
					92739DD82CC7AA1B00F5B1CF = {
						CreatedOnToolsVersion = 16.0;
					};
					92739DE82CC7AA1E00F5B1CF = {
						CreatedOnToolsVersion = 16.0;
						TestTargetID = 92739DD82CC7AA1B00F5B1CF;
					};
					92739DF22CC7AA1E00F5B1CF = {
						CreatedOnToolsVersion = 16.0;
						TestTargetID = 92739DD82CC7AA1B00F5B1CF;
					};
				};
			};
			buildConfigurationList = 92739DD42CC7AA1B00F5B1CF /* Build configuration list for PBXProject "Appio" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 92739DD02CC7AA1B00F5B1CF;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				927A88402E7A221A00D424E8 /* XCRemoteSwiftPackageReference "sentry-cocoa" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 92739DDA2CC7AA1B00F5B1CF /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				92739DD82CC7AA1B00F5B1CF /* Appio */,
				92739DE82CC7AA1E00F5B1CF /* AppioTests */,
				92739DF22CC7AA1E00F5B1CF /* AppioUITests */,
				9244A0712D81AC6E009F9C10 /* AppioNotificationServiceExtension */,
				9244A0822D81B028009F9C10 /* AppioNotificationContentExtension */,
				922E4B482DA011530069FF7E /* AppioWidgetExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		922E4B472DA011530069FF7E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9244A0702D81AC6E009F9C10 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9244A0812D81B028009F9C10 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		92739DD72CC7AA1B00F5B1CF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				926425442D479E4C00EE1630 /* app-clip.md in Resources */,
				923DA5992CCA953E00630F93 /* README.md in Resources */,
				9264254A2D479EC300EE1630 /* ios-sizes.md in Resources */,
				926425462D479E7100EE1630 /* clipboard-copy-paste.md in Resources */,
				9264253B2D479DC200EE1630 /* install-button-ios-versions.txt in Resources */,
				9264253C2D479DC200EE1630 /* Smart App Banner - OPEN.PNG in Resources */,
				927205582E1EAF1000A99E4B /* release.md in Resources */,
				921F01652D79BE4F0028254A /* opening-flow.md in Resources */,
				9264253D2D479DC200EE1630 /* Smart App Banner - Download.PNG in Resources */,
				9264254D2D47A06300EE1630 /* deep-linking.md in Resources */,
				9264253E2D479DC200EE1630 /* Smart App Banner.md in Resources */,
				9264253F2D479DC200EE1630 /* Smart App Banner - GET.PNG in Resources */,
				926425402D479DC200EE1630 /* Smart App Banner.MP4 in Resources */,
				926915C92E0467E50075ED37 /* ios-widget-config.md in Resources */,
				926425422D479DDC00EE1630 /* api.md in Resources */,
				92D4A2872D81D663004153B9 /* push-notifications.md in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		92739DE72CC7AA1E00F5B1CF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		92739DF12CC7AA1E00F5B1CF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		922E4B452DA011530069FF7E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9244A06E2D81AC6E009F9C10 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9244A07F2D81B028009F9C10 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		92739DD52CC7AA1B00F5B1CF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		92739DE52CC7AA1E00F5B1CF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		92739DEF2CC7AA1E00F5B1CF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		922E4B592DA011560069FF7E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 922E4B482DA011530069FF7E /* AppioWidgetExtension */;
			targetProxy = 922E4B582DA011560069FF7E /* PBXContainerItemProxy */;
		};
		9244A0782D81AC6E009F9C10 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9244A0712D81AC6E009F9C10 /* AppioNotificationServiceExtension */;
			targetProxy = 9244A0772D81AC6E009F9C10 /* PBXContainerItemProxy */;
		};
		9244A0912D81B028009F9C10 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9244A0822D81B028009F9C10 /* AppioNotificationContentExtension */;
			targetProxy = 9244A0902D81B028009F9C10 /* PBXContainerItemProxy */;
		};
		92739DEB2CC7AA1E00F5B1CF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 92739DD82CC7AA1B00F5B1CF /* Appio */;
			targetProxy = 92739DEA2CC7AA1E00F5B1CF /* PBXContainerItemProxy */;
		};
		92739DF52CC7AA1E00F5B1CF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 92739DD82CC7AA1B00F5B1CF /* Appio */;
			targetProxy = 92739DF42CC7AA1E00F5B1CF /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		922E4B5D2DA011560069FF7E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 921960B02E7AE0840003656D /* Configs */;
			baseConfigurationReferenceRelativePath = Debug.xcconfig;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = AppioWidget/AppioWidgetExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AppioWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = AppioWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.2;
				PRODUCT_BUNDLE_IDENTIFIER = so.appio.app.AppioWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		922E4B5E2DA011560069FF7E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 921960B02E7AE0840003656D /* Configs */;
			baseConfigurationReferenceRelativePath = Release.xcconfig;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = AppioWidget/AppioWidgetExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AppioWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = AppioWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.2;
				PRODUCT_BUNDLE_IDENTIFIER = so.appio.app.AppioWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		9244A07C2D81AC6E009F9C10 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 921960B02E7AE0840003656D /* Configs */;
			baseConfigurationReferenceRelativePath = Debug.xcconfig;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = AppioNotificationServiceExtension/AppioNotificationServiceExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AppioNotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = AppioNotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.2;
				PRODUCT_BUNDLE_IDENTIFIER = so.appio.app.AppioNotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		9244A07D2D81AC6E009F9C10 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 921960B02E7AE0840003656D /* Configs */;
			baseConfigurationReferenceRelativePath = Release.xcconfig;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = AppioNotificationServiceExtension/AppioNotificationServiceExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AppioNotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = AppioNotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.2;
				PRODUCT_BUNDLE_IDENTIFIER = so.appio.app.AppioNotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		9244A0952D81B028009F9C10 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 921960B02E7AE0840003656D /* Configs */;
			baseConfigurationReferenceRelativePath = Debug.xcconfig;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = AppioNotificationContentExtension/AppioNotificationContentExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AppioNotificationContentExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = AppioNotificationContentExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.2;
				PRODUCT_BUNDLE_IDENTIFIER = so.appio.app.AppioNotificationContentExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		9244A0962D81B028009F9C10 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 921960B02E7AE0840003656D /* Configs */;
			baseConfigurationReferenceRelativePath = Release.xcconfig;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = AppioNotificationContentExtension/AppioNotificationContentExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AppioNotificationContentExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = AppioNotificationContentExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.2;
				PRODUCT_BUNDLE_IDENTIFIER = so.appio.app.AppioNotificationContentExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		92739DFB2CC7AA1E00F5B1CF /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 921960B02E7AE0840003656D /* Configs */;
			baseConfigurationReferenceRelativePath = Debug.xcconfig;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = V9P4DCJF8Z;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		92739DFC2CC7AA1E00F5B1CF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 921960B02E7AE0840003656D /* Configs */;
			baseConfigurationReferenceRelativePath = Release.xcconfig;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = V9P4DCJF8Z;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		92739DFE2CC7AA1E00F5B1CF /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 921960B02E7AE0840003656D /* Configs */;
			baseConfigurationReferenceRelativePath = Debug.xcconfig;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "Appio App/Appio App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Appio App/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Appio-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = Appio;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.business";
				INFOPLIST_KEY_NSCameraUsageDescription = "We need camera access to scan QR codes";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UIUserInterfaceStyle = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.2;
				PRODUCT_BUNDLE_IDENTIFIER = so.appio.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		92739DFF2CC7AA1E00F5B1CF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 921960B02E7AE0840003656D /* Configs */;
			baseConfigurationReferenceRelativePath = Release.xcconfig;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "Appio App/Appio App.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Appio App/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Appio-App-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = Appio;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.business";
				INFOPLIST_KEY_NSCameraUsageDescription = "We need camera access to scan QR codes";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UIUserInterfaceStyle = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.2;
				PRODUCT_BUNDLE_IDENTIFIER = so.appio.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		92739E012CC7AA1E00F5B1CF /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 921960B02E7AE0840003656D /* Configs */;
			baseConfigurationReferenceRelativePath = Debug.xcconfig;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "so.appio.Appio-AppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Appio.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Appio";
			};
			name = Debug;
		};
		92739E022CC7AA1E00F5B1CF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 921960B02E7AE0840003656D /* Configs */;
			baseConfigurationReferenceRelativePath = Release.xcconfig;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "so.appio.Appio-AppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Appio.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Appio";
			};
			name = Release;
		};
		92739E042CC7AA1E00F5B1CF /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 921960B02E7AE0840003656D /* Configs */;
			baseConfigurationReferenceRelativePath = Debug.xcconfig;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "so.appio.Appio-AppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "Appio App";
			};
			name = Debug;
		};
		92739E052CC7AA1E00F5B1CF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 921960B02E7AE0840003656D /* Configs */;
			baseConfigurationReferenceRelativePath = Release.xcconfig;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "so.appio.Appio-AppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "Appio App";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		922E4B5C2DA011560069FF7E /* Build configuration list for PBXNativeTarget "AppioWidgetExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				922E4B5D2DA011560069FF7E /* Debug */,
				922E4B5E2DA011560069FF7E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9244A07B2D81AC6E009F9C10 /* Build configuration list for PBXNativeTarget "AppioNotificationServiceExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9244A07C2D81AC6E009F9C10 /* Debug */,
				9244A07D2D81AC6E009F9C10 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9244A0942D81B028009F9C10 /* Build configuration list for PBXNativeTarget "AppioNotificationContentExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9244A0952D81B028009F9C10 /* Debug */,
				9244A0962D81B028009F9C10 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		92739DD42CC7AA1B00F5B1CF /* Build configuration list for PBXProject "Appio" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				92739DFB2CC7AA1E00F5B1CF /* Debug */,
				92739DFC2CC7AA1E00F5B1CF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		92739DFD2CC7AA1E00F5B1CF /* Build configuration list for PBXNativeTarget "Appio" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				92739DFE2CC7AA1E00F5B1CF /* Debug */,
				92739DFF2CC7AA1E00F5B1CF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		92739E002CC7AA1E00F5B1CF /* Build configuration list for PBXNativeTarget "AppioTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				92739E012CC7AA1E00F5B1CF /* Debug */,
				92739E022CC7AA1E00F5B1CF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		92739E032CC7AA1E00F5B1CF /* Build configuration list for PBXNativeTarget "AppioUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				92739E042CC7AA1E00F5B1CF /* Debug */,
				92739E052CC7AA1E00F5B1CF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		927A88402E7A221A00D424E8 /* XCRemoteSwiftPackageReference "sentry-cocoa" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/getsentry/sentry-cocoa";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.56.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		927A88412E7A239400D424E8 /* Sentry */ = {
			isa = XCSwiftPackageProductDependency;
			package = 927A88402E7A221A00D424E8 /* XCRemoteSwiftPackageReference "sentry-cocoa" */;
			productName = Sentry;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 92739DD12CC7AA1B00F5B1CF /* Project object */;
}
