//
//  AppDelegate.swift
//  Appio App
//
//  Created by gondo on 22/10/2024.
//

import BackgroundTasks
import UIKit

class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_: UIApplication,
                     didFinishLaunchingWithOptions _: [UIApplication.LaunchOptionsKey: Any]? = nil) -> Bool
    {
        Log.shared.warning("App started")

        // Sentry
        SentryInit.start()
        
        // Register notification manager
        UNUserNotificationCenter.current().delegate = NotificationDelegate.shared

        // Register background fetch task
        BGTaskScheduler.shared.register(forTaskWithIdentifier: BackgroundFetchManager.identifier, using: nil) { task in
            BackgroundFetchManager.handleAppRefresh(task: task as! BGAppRefreshTask)
        }

        // Schedule refresh (here as well as when app goes to background)
        BackgroundFetchManager.scheduleAppRefresh() // backup suggested by AI

        // Register push notification actions
        PushNotificationManager.registerNotificationActions()

        // Widgets tutorial Picture in Picture
        WidgetTutorialAppDelegate.registerPIP()
        
        return true
    }
}

// MARK: - Notifications

extension AppDelegate {
    func application(_: UIApplication,
                     didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data)
    {
        PushNotificationManager.didRegisterForRemoteNotifications(deviceToken: deviceToken)
    }

    func application(_: UIApplication,
                     didFailToRegisterForRemoteNotificationsWithError error: Error)
    {
        PushNotificationManager.didFailToRegisterForRemoteNotifications(error: error)
    }

    // Process background notificaiton
    func application(_: UIApplication,
                     didReceiveRemoteNotification userInfo: [AnyHashable: Any],
                     fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void)
    {
        Log.shared.warning("📩 Silent push received with userInfo: \(userInfo)")

        BackgroundFetchManager.refreshData { success in
            if success {
                Log.shared.warning("✅ Widget updated from silent push")
                completionHandler(.newData)
            } else {
                Log.shared.warning("❌ Failed to update from silent push")
                completionHandler(.failed)
            }
        }
    }
}
