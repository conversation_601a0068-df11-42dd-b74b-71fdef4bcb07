//
//  APIDevice.swift
//  Appio
//
//  Created by gondo on 21/03/2025.
//

import Foundation
import UIKit

/**
 NOTE-name:
 UIDevice.current.name used to return custom name ("<PERSON><PERSON>s iPhone") but since iOS 16 it returns generic name "iPhone"
 https://developer.apple.com/documentation/uikit/uidevice/name
 */

struct NewDeviceRequest: Codable {
    let customerUserId: String
    let name: String = UIDevice.current.name // see NOTE-name above
    let platform: String = "ios"
    let osVersion: String = UIDevice.current.systemVersion
    let deviceIdentifier: String = UIDevice.current.deviceIdentifier
    let model: String
    let notificationsEnabled: Bool = false
    let deviceToken: String = "" // not known during registration

    // Custom mapping of JSON keys to struct properties
    enum CodingKeys: String, CodingKey {
        case name, platform, model
        case customerUserId = "customer_user_id"
        case osVersion = "os_version"
        case notificationsEnabled = "notifications_enabled"
        case deviceIdentifier = "device_identifier"
        case deviceToken = "device_token"
    }
}

struct UpdateDeviceRequest: Codable {
    let notificationsEnabled: Bool
    let deviceToken: String
    
    // Custom mapping of JSON keys to struct properties
    enum CodingKeys: String, CodingKey {
        case notificationsEnabled = "notifications_enabled"
        case deviceToken = "device_token"
    }
}

struct DeviceResponse: Codable, Equatable {
    let id: String
}
