//
//  Sentry.swift
//  Appio
//
//  Created by gondo on 17/09/2025.
//

import Sentry
import WidgetKit

enum SentryInit {
    static var started = false
    
    static func start() {
        Log.shared.error("Sentry started")
        
        guard !started else { return }
        started = true
        
        guard let escapedDsn = Bundle.main.object(forInfoDictionaryKey: "SENTRY_DSN") as? String else {
            Log.shared.error("Sentry DSN not found in Info.plist")
            return
        }
        let dsn = escapedDsn.replacingOccurrences(of: "\\", with: "")
        
        Log.shared.info("Sentry DSN: \(dsn)")
        
        SentrySDK.start { options in
            options.dsn = dsn
#if DEBUG
            options.debug = true
            options.environment = "debug"
#endif
            options.tracesSampleRate = 0.1
            options.sessionReplay.onErrorSampleRate = 1.0
            options.sessionReplay.sessionSampleRate = 0.1
            
            options.sendDefaultPii = true
            options.attachScreenshot = true
            options.attachViewHierarchy = true
        }
    }
}
