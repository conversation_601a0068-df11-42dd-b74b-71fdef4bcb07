//
//  StringExtension.swift
//  Appio
//
//  Created by gondo on 29/03/2025.
//

import SwiftUI

extension String {
    func truncated(to length: Int) -> String {
        return count > length ? String(prefix(length)) : self // …
    }

    func truncatedHumanReadable(to length: Int, trailing: String = "…") -> String {
        // If the string is already short, return it as is.
        guard count > length else { return self }

        // Get the substring up to the specified length.
        let indexAtLength = index(startIndex, offsetBy: length)
        var truncated = String(self[..<indexAtLength])

        // Find the last whitespace to avoid cutting a word in half.
        if let lastSpaceIndex = truncated.lastIndex(of: " ") {
            truncated = String(truncated[..<lastSpaceIndex])
        }

        return truncated + trailing
    }
    
    func toColor(colorScheme: ColorScheme, fallback: Color) -> Color {
        switch lowercased() {
        case "accentColor": return .accentColor
        case "black": return .black
        case "blue": return .blue
        case "brown": return .brown
        case "clear": return .clear
        case "cyan": return .cyan
        case "gray": return .gray
        case "green": return .green
        case "indigo": return .indigo
        case "mint": return .mint
        case "orange": return .orange
        case "pink": return .pink
        case "primary": return .primary
        case "purple": return .purple
        case "red": return .red
        case "secondary": return .secondary
        case "teal": return .teal
        case "white": return .white
        case "yellow": return .yellow
            
        // Matching Android colors
        case "aqua": return colorScheme == .dark ? Color(hex: "#66FFFF")! : Color(hex: "#00FFFF")!
        case "darkgray": return colorScheme == .dark ? Color(hex: "#D3D3D3")! : Color(hex: "#A9A9A9")!
        case "darkgrey": return colorScheme == .dark ? Color(hex: "#D3D3D3")! : Color(hex: "#A9A9A9")!
        case "fuchsia": return colorScheme == .dark ? Color(hex: "#FF66FF")! : Color(hex: "#FF00FF")!
        case "grey": return colorScheme == .dark ? Color(hex: "#B0B0B0")! : Color(hex: "#808080")!
        case "lightgray": return colorScheme == .dark ? Color(hex: "#E8E8E8")! : Color(hex: "#D3D3D3")!
        case "lightgrey": return colorScheme == .dark ? Color(hex: "#E8E8E8")! : Color(hex: "#D3D3D3")!
        case "lime": return colorScheme == .dark ? Color(hex: "#66FF66")! : Color(hex: "#00FF00")!
        case "maroon": return colorScheme == .dark ? Color(hex: "#B00030")! : Color(hex: "#800000")!
        case "navy": return colorScheme == .dark ? Color(hex: "#333399")! : Color(hex: "#000080")!
        case "olive": return colorScheme == .dark ? Color(hex: "#B0B050")! : Color(hex: "#808000")!
        case "silver": return colorScheme == .dark ? Color(hex: "#E0E0E0")! : Color(hex: "#C0C0C0")!
        case "magenta": return colorScheme == .dark ? Color(hex: "#FF66FF")! : Color(hex: "#FF00FF")!
            
        default:
            let colors = split(separator: ",")
            if colors.count == 2 && colorScheme == .dark {
                return Color(hex: String(colors[1])) ?? fallback
            }
            return Color(hex: String(colors[0])) ?? fallback
        }
    }
}
